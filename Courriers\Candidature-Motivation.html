<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Proposition de Collaboration - <PERSON></title>
    <!-- Chargement de Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Chargement de GSAP et du plugin ScrollTrigger -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
    <!-- Importation de la police Manrope depuis Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@400;500;700;800&display=swap" rel="stylesheet">
    <style>
        /* Définition des couleurs personnalisées EXACTES */
        :root {
            --brand-cyan: #0EA5E9;
            --brand-orange: #FE9537;
            --brand-dark: #0D0D0D;
            --brand-light: #E0E0E0;
            --brand-grey: #2a2a2a;
        }
        
        body {
            font-family: 'Manrope', sans-serif;
        }

        .text-brand-cyan { color: var(--brand-cyan); }
        .text-brand-orange { color: var(--brand-orange); }
        .text-brand-light { color: var(--brand-light); }
        .border-brand-orange { border-color: var(--brand-orange); }
        .border-brand-grey { border-color: var(--brand-grey); }

        /* Effet de survol pour les liens */
        .hover-glow {
            transition: all 0.3s ease;
        }
        .hover-glow:hover {
            transform: scale(1.1) translateY(-2px);
            filter: drop-shadow(0 0 8px var(--brand-orange));
        }
        
        /* Style pour la bannière du header SANS EFFETS */
        .header-banner {
            background-image: url('https://res.cloudinary.com/dte5zykne/image/upload/v1750807610/1-banniere-image_awthzw.png');
            background-size: cover;
            background-position: center;
        }
        
        /* Style pour la bannière du footer SANS EFFETS et avec position ajustée */
        .footer-banner {
            background-image: url('https://res.cloudinary.com/dte5zykne/image/upload/v1750807706/Bottom-Banniere_rtro7k.png');
            background-size: cover;
            background-position: top;
        }

        /* Style pour la vidéo en arrière-plan */
        .video-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: -1;
        }

        /* Classe pour les éléments à animer au scroll (initialement invisibles) */
        .animate-on-scroll {
            opacity: 0;
        }
    </style>
</head>
<body class="no-js antialiased bg-brand-dark">
        /* Fallback : si JS ne fonctionne pas, le texte reste visible */
        .no-js .animate-on-scroll { opacity: 1 !important; }

    <!-- VIDÉO EN ARRIÈRE-PLAN -->
    <video autoplay loop muted playsinline class="video-background">
        <source src="https://videos.pexels.com/video-files/9665235/9665235-hd_1920_1080_25fps.mp4" type="video/mp4">
        Votre navigateur ne supporte pas la balise vidéo.
    </video>
    <!-- Voile sombre sur la vidéo pour la lisibilité -->
    <div class="fixed inset-0 bg-black opacity-60 z-[-1]"></div>


    <!-- Le conteneur principal avec la structure et l'image de fond FIXE -->
    <div id="main-container" class="container mx-auto max-w-4xl my-8 border border-brand-grey rounded-lg shadow-2xl shadow-brand-orange/10 overflow-hidden flex flex-col" 
         style="background-image: url('https://res.cloudinary.com/dte5zykne/image/upload/v1751471696/mur-marron-fonce-6_gik7qe.jpg'); background-size: cover; background-position: center; background-attachment: fixed;">
        
        <!-- Voile d'assombrissement qui sera contrôlé par JavaScript -->
        <div id="scroll-overlay" class="absolute inset-0 bg-black transition-opacity duration-300 pointer-events-none" style="opacity: 0;"></div>

        <!-- Contenu principal positionné au-dessus du voile -->
        <div class="relative z-10 flex flex-col">
            <!-- EN-TÊTE AVEC BANNIÈRE -->
            <header class="header-banner p-8 md:p-12">
                <div class="flex justify-between items-start">
                    <div id="header-logo-placeholder">
                        <img src="https://res.cloudinary.com/dte5zykne/image/upload/v1750807840/02-Logo-FlexoDiv_uoxcao.png" alt="Logo FlexoDiv" class="h-16 md:h-16">
                    </div>
                    <div class="text-right">
                        <h1 class="text-2xl sm:text-3xl font-extrabold text-white">François Guinta</h1>
                        <p class="text-sm text-gray-300">6 rue du bout du monde<br>03340 Saint-Gérand-de-Vaux</p>
                        <!-- TITRES PROFESSIONNELS AJOUTÉS -->
                        <p class="text-sm font-semibold text-brand-orange mt-2">Développeur Créatif & Architecte de Solutions IA</p>
                        <p class="text-xs text-gray-300 mt-1">Prototypage Web | Ingénierie Inverse | Full-Stack | Prompt Engineering Avancé</p>
                    </div>
                </div>
                 <div class="mt-8 text-left">
                    <p class="text-lg font-semibold text-white">Aux équipes de recrutement</p>
                    <p class="text-sm text-gray-300">Spécialistes des métiers de la Tech et de l'Innovation</p>
                </div>
            </header>

            <!-- Contenu de la lettre -->
            <div class="flex-grow p-8 md:p-12">
                <!-- OBJET -->
                <div class="mb-12 animate-on-scroll">
                    <h2 class="text-xl sm:text-2xl font-bold text-brand-orange border-b-2 border-brand-grey/50 pb-3">
                        Objet : Proposition de collaboration <br> Développeur d'Expériences Interactives & Intégrateur IA (LLM)
                    </h2>
                    <p id="date-location" class="text-right mt-2 text-sm text-gray-400">Fait à Saint-Gérand-de-Vaux, le 31 juillet 2025</p>
                </div>

                <!-- CORPS DE LA LETTRE -->
                <main class="space-y-6 text-base text-brand-light leading-relaxed">
                    <p class="animate-on-scroll">Madame, Monsieur,</p>
                    <p class="animate-on-scroll">
                        Face à la complexité croissante des projets digitaux, les entreprises ont besoin de profils qui transcendent les rôles traditionnels. Mon expertise se situe précisément à cette intersection : un développeur capable non seulement de maîtriser les technologies de pointe, mais aussi de pratiquer l'<strong>ingénierie inversée</strong> et le <strong>prototypage rapide</strong> pour transformer une idée en une solution fonctionnelle.
                    </p>
                    <p class="animate-on-scroll">
                        Fort d'une solide expérience en développement Front-End avec la stack **React, TypeScript, Tailwind CSS, GSAP, et Three.js/R3F**, je conçois et déploie des solutions sur mesure où l'IA (LLM) est au service de l'expérience utilisateur. Mon approche est orientée vers le <strong class="text-brand-orange">retour sur investissement</strong> et la création de valeur tangible.
                    </p>
                    <div class="border-l-4 border-brand-orange pl-4 my-8 bg-black bg-opacity-20 p-4 rounded-r-lg animate-on-scroll">
                        <p class="italic">
                            Organisé pour une efficacité maximale, je suis équipé pour le <strong class="text-white">100% télétravail</strong>, garantissant une réactivité et une disponibilité optimales pour des missions sur l'ensemble du territoire national.
                        </p>
                    </div>
                    <!-- POINTS CLÉS -->
                    <div class="bg-gray-900/50 border border-brand-grey rounded-lg p-6 mt-8 animate-on-scroll">
                        <h3 class="font-bold text-lg text-white mb-4">Mes atouts différenciants :</h3>
                        <ul class="list-none space-y-4">
                            <li class="flex items-start">
                                <span class="text-brand-orange mr-4 mt-1">◆</span>
                                <div><strong class="text-white">Auto-apprentissage accéléré par l'IA :</strong> Je ne me contente pas d'apprendre ; je construis des outils qui m'enseignent. Face à une nouvelle technologie, je la relève comme un défi en créant des agents IA qui me forment en temps réel. C'est une méthode <strong class="text-brand-orange">extrêmement rare</strong> qui garantit une maîtrise rapide et profonde de n'importe quel sujet technique.</div>
                            </li>
                            <li class="flex items-start">
                                <span class="text-brand-orange mr-4 mt-1">◆</span>
                                <div><strong class="text-white">Développeur d'Expériences Interactives & IA :</strong> Je ne me limite pas à l'intégration. Je conçois et développe des expériences web immersives complètes, en 2D et 3D (avec Three.js), qui placent l'intelligence artificielle au cœur de l'interaction utilisateur.</div>
                            </li>
                             <li class="flex items-start">
                                <span class="text-brand-orange mr-4 mt-1">◆</span>
                                <div><strong class="text-white">Vision "Solution" :</strong> Je traduis les besoins business en outils concrets qui optimisent la productivité, réduisent les coûts et ouvrent de nouvelles opportunités.</div>
                            </li>
                        </ul>
                    </div>
                    <p class="animate-on-scroll">
                        Je suis actuellement à l'écoute du marché et j'étudie avec attention toute proposition (intérim, freelance, CDD/CDI) où mes compétences pourraient être un atout stratégique pour vos clients.
                    </p>
                    <p class="animate-on-scroll">Dans l'attente de votre retour, je vous prie d'agréer, Madame, Monsieur, l'expression de mes salutations distinguées.</p>
                    <div class="flex justify-end items-end space-x-8 pt-6">
                        <img id="portrait-medallion" src="https://res.cloudinary.com/dte5zykne/image/upload/v1751477105/Portrait-Cisco-FlexoDiv-DhdYrlGh_xq0jfm.png" alt="Portrait de François" class="h-24 rounded-full border-2 border-brand-orange/50">
                        <div class="text-right font-bold text-white">
                            <p id="signature-name" class="animate-on-scroll">François Guinta</p>
                            <div id="signature-placeholder" class="mt-2 h-16 flex justify-end">
                                 <img src="https://res.cloudinary.com/dte5zykne/image/upload/v1750807998/PENUP_2023_222227_uzb5eh.png" alt="Signature de François" class="h-16">
                            </div>
                        </div>
                    </div>
                </main>
            </div>
            
            <!-- FOOTER -->
            <footer class="footer-banner p-24 md:p-24 text-center text-gray-200 mt-auto">
                <div class="transform translate-y-4">
                    <p class="font-bold text-lg text-white mb-6">Découvrez mon univers</p>
                    <div class="flex justify-center items-center space-x-6">
                        <a id="footer-link-portfolio" href="https://flexodiv.netlify.app/" target="_blank" class="flex items-center space-x-2 text-white hover-glow font-semibold">
                            <img src="https://res.cloudinary.com/dte5zykne/image/upload/v1750807737/Tamise-Lumiere-Logo-FlexoDiv_tz504e.png" alt="Logo Médaillon" class="h-10">
                            <span>Portfolio</span>
                        </a>
                        <a id="footer-link-linkedin" href="https://www.linkedin.com/in/flexodiv-developpeur-de-solutions-ia-982582203/" target="_blank" class="hover-glow">
                            <img src="https://res.cloudinary.com/dte5zykne/image/upload/v1750807904/FGH145_yvzprn.png" alt="Logo LinkedIn" class="h-8">
                        </a>
                        <a id="footer-link-youtube" href="https://www.youtube.com/@flexodiv" target="_blank" class="hover-glow">
                            <img src="https://res.cloudinary.com/dte5zykne/image/upload/v1750807899/fggh57_v8rmzk.png" alt="Logo YouTube" class="h-8">
                        </a>
                        <a id="footer-link-email" href="mailto:<EMAIL>" class="hover-glow">
                             <img src="https://res.cloudinary.com/dte5zykne/image/upload/v1750809013/gmail_bt1hbc.png" alt="Logo Gmail" class="h-8">
                        </a>
                    </div>
                </div>
            </footer>
        </div>
    </div>

    <script>
      document.body.classList.remove('no-js');
        document.addEventListener('DOMContentLoaded', () => {
            gsap.registerPlugin(ScrollTrigger);

            // SCRIPT POUR L'ASSOMBRISSEMENT AU SCROLL
            const overlay = document.getElementById('scroll-overlay');
            // L'écouteur est sur la fenêtre globale car le body scrolle
            window.addEventListener('scroll', () => {
                const scrollHeight = document.documentElement.scrollHeight;
                const clientHeight = document.documentElement.clientHeight;
                const scrollTop = window.scrollY;
                
                const maxScrollTop = scrollHeight - clientHeight;
                const scrollFraction = maxScrollTop > 0 ? scrollTop / maxScrollTop : 0;
                
                // Opacité max de 0.65 pour ne pas rendre le fond totalement noir
                overlay.style.opacity = Math.min(scrollFraction * 0.65, 0.65);
            });

            // ANIMATIONS GSAP AU SCROLL
            // Animation pour tous les éléments de texte
            gsap.utils.toArray('.animate-on-scroll').forEach(elem => {
                gsap.from(elem, {
                    scrollTrigger: {
                        trigger: elem,
                        // Le scroller par défaut (la fenêtre) est utilisé, donc la ligne est retirée
                        start: 'top 90%', 
                        toggleActions: 'play none none none',
                    },
                    y: 50,
                    opacity: 0,
                    duration: 1,
                    ease: 'power3.out'
                });
            });

            // Animation du portrait
            gsap.from("#portrait-medallion", { 
                scrollTrigger: {
                    trigger: "#portrait-medallion",
                    start: 'top 95%',
                    toggleActions: 'play none none none',
                },
                duration: 1.2, 
                x: -150, 
                opacity: 0, 
                ease: "power3.out",
            });

            // Animation de la signature
            gsap.from("#signature-placeholder", { 
                scrollTrigger: {
                    trigger: "#signature-placeholder",
                    start: 'top 95%',
                    toggleActions: 'play none none none',
                },
                duration: 1.5, 
                opacity: 0, 
                ease: "power2.inOut",
            });
        });
    </script>

</body>
</html>
